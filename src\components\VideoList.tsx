import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import VideoItem from "@/components/VideoItem"
import type { VideoListProps } from "@/types"
import { setRequestHeadersForExtension } from "@/utils"

// 全局状态存储：按标签页ID存储展开的视频requestId
const expandedVideosByTab = new Map<number, string | null>()

export default function VideoList({ videos, className = "", tabId }: VideoListProps) {
  const { t } = useTranslation()
  // 使用requestId而不是索引来标识展开的视频
  const [expandedVideoRequestId, setExpandedVideoRequestId] = useState<string | null>(null)

  // 当标签页ID变化时，恢复该标签页的展开状态
  useEffect(() => {
    if (tabId !== undefined) {
      const savedExpandedRequestId = expandedVideosByTab.get(tabId) || null
      setExpandedVideoRequestId(savedExpandedRequestId)
    }
  }, [tabId])

  // 保存展开状态到全局存储
  const saveExpandedState = (requestId: string | null) => {
    if (tabId !== undefined) {
      expandedVideosByTab.set(tabId, requestId)
    }
    setExpandedVideoRequestId(requestId)
  }

  const handlePlayClick = async (index: number) => {
    const video = videos[index]
    const videoRequestId = video.requestId

    // 如果点击的是当前展开的视频，则收起
    if (expandedVideoRequestId === videoRequestId) {
      saveExpandedState(null)
      return
    }

    // 如果有请求头，先设置扩展页面请求头规则
    if (video.requestHeaders && video.requestHeaders.length > 0) {
      try {
        const success = await setRequestHeadersForExtension(
          videoRequestId,
          video.url,
          video.requestHeaders,
          video.pageUrl
        )

        if (success) {
          console.log('已为扩展页面预览设置请求头，数量:', video.requestHeaders.length)
        } else {
          console.warn('设置扩展页面请求头失败')
        }
      } catch (error) {
        console.error('设置扩展页面请求头失败:', error)
      }
    }

    // 展开新的视频
    saveExpandedState(videoRequestId)
  }

  return (
    <div className={`flex flex-col items-start gap-2.5 w-full ${className}`}>
      {/* 视频列表 */}
      <div className="flex flex-col items-start gap-2.5 w-full">
        {videos.map((video, index) => (
          <VideoItem
            key={video.requestId}
            video={video}
            showThumbnail={expandedVideoRequestId === video.requestId}
            onPlayClick={() => handlePlayClick(index)}
          />
        ))}
      </div>

      {/* 提示文本 */}
      <div className="flex justify-center items-center w-full">
        <div className="text-xs font-normal text-gray-500 text-center leading-relaxed">
          {t('videoList.noTargetVideo')}<br />
          {t('videoList.tryPasteLink')}<a
            href="https://snapany.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:text-blue-600"
          >
            {t('videoList.pasteToDownload')}
          </a>
        </div>
      </div>
    </div>
  )
} 